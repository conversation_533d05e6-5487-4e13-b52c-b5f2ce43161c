/**
 * 代码验证测试脚本
 * 用于测试 CodeUpdater 的验证功能
 */

const CodeUpdater = require('../codeUpdater');

// 创建代码更新器实例
const codeUpdater = new CodeUpdater();

// 测试用例
const testCases = [
  {
    name: '正常的 Playwright 代码',
    code: `
// 导航到网页
await page.goto('https://www.baidu.com');

// 等待页面加载
await page.waitForLoadState('networkidle');

// 获取页面标题
const title = await page.title();
console.log('页面标题:', title);

// 返回结果
return { title, url: page.url() };
    `,
    shouldPass: true
  },
  {
    name: '包含危险关键词的代码',
    code: `
eval('console.log("危险代码")');
    `,
    shouldPass: false
  },
  {
    name: '简单的页面操作',
    code: `
await page.click('#search-button');
const text = await page.textContent('.result');
return text;
    `,
    shouldPass: true
  },
  {
    name: '包含 console.log 的代码',
    code: `
console.log('开始测试');
await page.goto('https://example.com');
console.log('页面加载完成');
    `,
    shouldPass: true
  },
  {
    name: '语法错误的代码',
    code: `
await page.goto('https://example.com'
// 缺少分号和括号
const title = await page.title(;
    `,
    shouldPass: false
  }
];

// 运行测试
console.log('🧪 开始代码验证测试...\n');

testCases.forEach((testCase, index) => {
  console.log(`测试 ${index + 1}: ${testCase.name}`);
  console.log('代码:', testCase.code.trim());
  
  const result = codeUpdater.validateCode(testCase.code);
  
  console.log('验证结果:', {
    valid: result.valid,
    errors: result.errors,
    warnings: result.warnings
  });
  
  const passed = result.valid === testCase.shouldPass;
  console.log(`结果: ${passed ? '✅ 通过' : '❌ 失败'}`);
  
  if (!passed) {
    console.log(`期望: ${testCase.shouldPass ? '通过' : '失败'}, 实际: ${result.valid ? '通过' : '失败'}`);
  }
  
  console.log('---\n');
});

console.log('🏁 测试完成！');

// 测试默认代码
console.log('\n🔍 测试前端默认代码...');
const defaultCode = `// Playwright 自动化代码示例
// 可用的全局变量: page, context, browser

// 导航到网页
await page.goto('https://www.baidu.com');

// 等待页面加载
await page.waitForLoadState('networkidle');

// 获取页面标题
const title = await page.title();
console.log('页面标题:', title);

// 返回结果
return { title, url: page.url() };`;

const defaultResult = codeUpdater.validateCode(defaultCode);
console.log('默认代码验证结果:', {
  valid: defaultResult.valid,
  errors: defaultResult.errors,
  warnings: defaultResult.warnings
});

if (defaultResult.valid) {
  console.log('✅ 默认代码验证通过！');
} else {
  console.log('❌ 默认代码验证失败！');
  console.log('错误:', defaultResult.errors);
}
