/**
 * API 测试脚本
 * 测试后端 API 接口是否正常工作
 */

const API_BASE = 'http://localhost:8082';

// 测试健康检查
async function testHealth() {
  console.log('🔍 测试健康检查...');
  try {
    const response = await fetch(`${API_BASE}/health`);
    const data = await response.json();
    console.log('健康检查结果:', data);
    return data.status === 'ok';
  } catch (error) {
    console.error('健康检查失败:', error.message);
    return false;
  }
}

// 测试代码验证
async function testCodeValidation() {
  console.log('\n🧪 测试代码验证...');
  
  const testCode = `
// 测试代码
await page.goto('https://www.baidu.com');
const title = await page.title();
console.log('页面标题:', title);
return { title };
  `;

  try {
    const response = await fetch(`${API_BASE}/execute-step`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        code: testCode,
        stepName: '测试步骤',
        description: '这是一个测试步骤'
      })
    });

    const data = await response.json();
    console.log('代码验证结果:', data);
    
    if (data.success) {
      console.log('✅ 代码验证通过');
      return true;
    } else {
      console.log('❌ 代码验证失败:', data.message);
      if (data.errors) {
        console.log('错误详情:', data.errors);
      }
      return false;
    }
  } catch (error) {
    console.error('❌ API 调用失败:', error.message);
    return false;
  }
}

// 测试获取步骤列表
async function testGetSteps() {
  console.log('\n📋 测试获取步骤列表...');
  try {
    const response = await fetch(`${API_BASE}/steps`);
    const data = await response.json();
    console.log('步骤列表结果:', data);
    return data.success;
  } catch (error) {
    console.error('获取步骤列表失败:', error.message);
    return false;
  }
}

// 测试获取系统状态
async function testGetStatus() {
  console.log('\n📊 测试获取系统状态...');
  try {
    const response = await fetch(`${API_BASE}/status`);
    const data = await response.json();
    console.log('系统状态结果:', data);
    return data.success;
  } catch (error) {
    console.error('获取系统状态失败:', error.message);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始 API 测试...\n');
  
  const results = [];
  
  // 运行所有测试
  results.push(await testHealth());
  results.push(await testCodeValidation());
  results.push(await testGetSteps());
  results.push(await testGetStatus());
  
  // 统计结果
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log('\n📈 测试结果统计:');
  console.log(`通过: ${passed}/${total}`);
  console.log(`成功率: ${(passed/total*100).toFixed(1)}%`);
  
  if (passed === total) {
    console.log('🎉 所有测试通过！');
  } else {
    console.log('⚠️  部分测试失败，请检查服务器状态');
  }
}

// 运行测试
runTests().catch(console.error);
