const { expect } = require('@playwright/test');

console.log('expect 函数:', expect);
console.log('expect 类型:', typeof expect);

// 测试 expect 是否能正常工作
try {
  expect(1).toBeGreaterThan(0);
  console.log('✅ expect 函数工作正常');
} catch (error) {
  console.error('❌ expect 函数测试失败:', error);
}

// 测试在 Function 构造函数中使用 expect
try {
  const testFunction = new Function(
    'expect',
    `
    expect(2).toBeGreaterThan(1);
    return 'success';
    `
  );
  
  const result = testFunction(expect);
  console.log('✅ Function 构造函数中的 expect 工作正常:', result);
} catch (error) {
  console.error('❌ Function 构造函数中的 expect 测试失败:', error);
}
