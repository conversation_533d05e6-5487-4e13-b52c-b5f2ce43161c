/**
 * 代码更新器
 * 负责代码的验证、热更新和安全检查
 */
class CodeUpdater {
  constructor() {
    this.codeHistory = [];
    this.maxHistorySize = 100;
    this.bannedKeywords = [
      'eval', 'Function',
      'process.exit', 'process.kill',
      'child_process', 'fs.unlink', 'fs.rmdir'
    ];
    this.allowedGlobals = [
      'console', 'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval',
      'Promise', 'Date', 'Math', 'JSON', 'Object', 'Array', 'String', 'Number', 'Boolean'
    ];
  }

  /**
   * 验证代码安全性和语法
   * @param {string} code - 要验证的代码
   */
  validateCode(code) {
    const errors = [];
    const warnings = [];

    try {
      // 基本安全检查
      const securityCheck = this.performSecurityCheck(code);
      if (!securityCheck.safe) {
        errors.push(...securityCheck.errors);
      }
      warnings.push(...securityCheck.warnings);

      // 语法检查
      const syntaxCheck = this.performSyntaxCheck(code);
      if (!syntaxCheck.valid) {
        errors.push(...syntaxCheck.errors);
      }

      // Playwright API 检查
      const playwrightCheck = this.performPlaywrightCheck(code);
      warnings.push(...playwrightCheck.warnings);

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        suggestions: this.generateSuggestions(code)
      };

    } catch (error) {
      return {
        valid: false,
        errors: [`代码验证失败: ${error.message}`],
        warnings: [],
        suggestions: []
      };
    }
  }

  /**
   * 执行安全检查
   * @param {string} code - 代码
   */
  performSecurityCheck(code) {
    const errors = [];
    const warnings = [];

    // 检查禁用关键词（更精确的匹配）
    this.bannedKeywords.forEach(keyword => {
      // 使用更精确的正则表达式匹配，避免误报
      const regex = new RegExp(`\\b${keyword.replace('.', '\\.')}\\b`, 'g');
      if (regex.test(code)) {
        errors.push(`禁止使用关键词: ${keyword}`);
      }
    });

    // 检查危险模式
    const dangerousPatterns = [
      /eval\s*\(/g,
      /new\s+Function\s*\(/g,
      /document\.write\s*\(/g,
      /innerHTML\s*=/g,
      /outerHTML\s*=/g,
      /\.exec\s*\(/g,
      /\.system\s*\(/g
    ];

    dangerousPatterns.forEach(pattern => {
      if (pattern.test(code)) {
        errors.push(`检测到危险模式: ${pattern.source}`);
      }
    });

    // 检查可疑操作
    const suspiciousPatterns = [
      /window\./g,
      /document\./g,
      /location\./g,
      /history\./g
    ];

    suspiciousPatterns.forEach(pattern => {
      if (pattern.test(code)) {
        warnings.push(`检测到可疑操作: ${pattern.source}，请确保这是预期行为`);
      }
    });

    return {
      safe: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 执行语法检查
   * @param {string} code - 代码
   */
  performSyntaxCheck(code) {
    const errors = [];

    try {
      // 尝试解析代码语法（包装在async函数中以支持await）
      new Function(`async function testCode() { ${code} }`);
    } catch (syntaxError) {
      // 只报告真正的语法错误，忽略一些常见的误报
      const errorMessage = syntaxError.message;
      if (!errorMessage.includes('Unexpected token') ||
          !errorMessage.includes('await') ||
          !errorMessage.includes('async')) {
        errors.push(`语法错误: ${errorMessage}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 执行 Playwright API 检查
   * @param {string} code - 代码
   */
  performPlaywrightCheck(code) {
    const warnings = [];
    const suggestions = [];

    // 检查常用的 Playwright 方法
    const playwrightMethods = [
      'page.goto', 'page.click', 'page.fill', 'page.type',
      'page.waitForSelector', 'page.screenshot', 'page.evaluate'
    ];

    const usedMethods = playwrightMethods.filter(method => code.includes(method));
    
    if (usedMethods.length === 0 && code.trim().length > 0) {
      warnings.push('代码中未检测到 Playwright API 调用，请确认这是预期行为');
    }

    // 检查异步操作
    if (code.includes('page.') && !code.includes('await') && !code.includes('.then(')) {
      warnings.push('检测到 Playwright API 调用但未使用 await，可能导致异步问题');
      suggestions.push('在 Playwright API 调用前添加 await 关键字');
    }

    // 检查选择器
    const selectorPatterns = [
      /'[^']*'/g,
      /"[^"]*"/g,
      /`[^`]*`/g
    ];

    selectorPatterns.forEach(pattern => {
      const matches = code.match(pattern);
      if (matches) {
        matches.forEach(match => {
          if (match.includes('#') || match.includes('.') || match.includes('[')) {
            // 看起来像CSS选择器，提供建议
            suggestions.push(`选择器 ${match} 建议使用更具体的定位方式`);
          }
        });
      }
    });

    return {
      warnings,
      suggestions
    };
  }

  /**
   * 生成代码建议
   * @param {string} code - 代码
   */
  generateSuggestions(code) {
    const suggestions = [];

    // 性能建议
    if (code.includes('page.waitForTimeout')) {
      suggestions.push('建议使用 page.waitForSelector 替代 page.waitForTimeout 以提高稳定性');
    }

    // 最佳实践建议
    if (code.includes('page.click') && !code.includes('waitForSelector')) {
      suggestions.push('建议在点击前使用 waitForSelector 确保元素存在');
    }

    if (code.includes('page.fill') && !code.includes('page.click')) {
      suggestions.push('填写表单后建议添加提交或确认操作');
    }

    // 错误处理建议
    if (!code.includes('try') && !code.includes('catch')) {
      suggestions.push('建议添加错误处理机制（try-catch）');
    }

    return suggestions;
  }

  /**
   * 更新代码
   * @param {string} code - 新代码
   * @param {Object} options - 更新选项
   */
  updateCode(code, options = {}) {
    try {
      // 验证代码
      const validation = this.validateCode(code);
      if (!validation.valid && !options.force) {
        return {
          success: false,
          message: '代码验证失败',
          errors: validation.errors,
          warnings: validation.warnings
        };
      }

      // 保存到历史记录
      this.saveToHistory(code);

      // 预处理代码
      const processedCode = this.preprocessCode(code);

      return {
        success: true,
        message: '代码更新成功',
        processedCode,
        warnings: validation.warnings,
        suggestions: validation.suggestions
      };

    } catch (error) {
      return {
        success: false,
        message: '代码更新失败',
        error: error.message
      };
    }
  }

  /**
   * 预处理代码
   * @param {string} code - 原始代码
   */
  preprocessCode(code) {
    let processedCode = code;

    // 添加默认的错误处理
    if (!code.includes('try') && !code.includes('catch')) {
      processedCode = `
try {
  ${processedCode}
} catch (error) {
  console.error('步骤执行出错:', error);
  throw error;
}
      `.trim();
    }

    // 添加执行日志
    processedCode = `
console.log('开始执行步骤...');
${processedCode}
console.log('步骤执行完成');
    `.trim();

    return processedCode;
  }

  /**
   * 保存到历史记录
   * @param {string} code - 代码
   */
  saveToHistory(code) {
    const historyItem = {
      code,
      timestamp: new Date().toISOString(),
      hash: this.generateHash(code)
    };

    this.codeHistory.unshift(historyItem);

    // 限制历史记录大小
    if (this.codeHistory.length > this.maxHistorySize) {
      this.codeHistory = this.codeHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * 生成代码哈希
   * @param {string} code - 代码
   */
  generateHash(code) {
    let hash = 0;
    for (let i = 0; i < code.length; i++) {
      const char = code.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }

  /**
   * 获取代码历史
   * @param {number} limit - 限制数量
   */
  getCodeHistory(limit = 10) {
    return this.codeHistory.slice(0, limit);
  }

  /**
   * 清除代码历史
   */
  clearCodeHistory() {
    this.codeHistory = [];
  }

  /**
   * 回滚到历史版本
   * @param {number} index - 历史索引
   */
  rollbackToHistory(index) {
    if (index >= 0 && index < this.codeHistory.length) {
      const historyItem = this.codeHistory[index];
      return {
        success: true,
        code: historyItem.code,
        timestamp: historyItem.timestamp
      };
    }
    return {
      success: false,
      message: '历史版本不存在'
    };
  }

  /**
   * 代码格式化
   * @param {string} code - 代码
   */
  formatCode(code) {
    try {
      // 简单的代码格式化
      let formatted = code
        .replace(/;\s*\n/g, ';\n')
        .replace(/{\s*\n/g, '{\n')
        .replace(/}\s*\n/g, '}\n')
        .replace(/,\s*\n/g, ',\n');

      // 添加适当的缩进
      const lines = formatted.split('\n');
      let indentLevel = 0;
      const indentSize = 2;

      const formattedLines = lines.map(line => {
        const trimmed = line.trim();
        if (!trimmed) return '';

        if (trimmed.includes('}')) {
          indentLevel = Math.max(0, indentLevel - 1);
        }

        const indentedLine = ' '.repeat(indentLevel * indentSize) + trimmed;

        if (trimmed.includes('{')) {
          indentLevel++;
        }

        return indentedLine;
      });

      return formattedLines.join('\n');
    } catch (error) {
      console.warn('代码格式化失败:', error);
      return code;
    }
  }

  /**
   * 代码压缩
   * @param {string} code - 代码
   */
  minifyCode(code) {
    try {
      return code
        .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
        .replace(/\/\/.*$/gm, '') // 移除行注释
        .replace(/\s+/g, ' ') // 压缩空白字符
        .trim();
    } catch (error) {
      console.warn('代码压缩失败:', error);
      return code;
    }
  }

  /**
   * 获取代码统计信息
   * @param {string} code - 代码
   */
  getCodeStatistics(code) {
    const lines = code.split('\n');
    const nonEmptyLines = lines.filter(line => line.trim().length > 0);
    const commentLines = lines.filter(line => line.trim().startsWith('//') || line.trim().startsWith('/*'));

    return {
      totalLines: lines.length,
      codeLines: nonEmptyLines.length,
      commentLines: commentLines.length,
      characters: code.length,
      words: code.split(/\s+/).filter(word => word.length > 0).length
    };
  }
}

module.exports = CodeUpdater;
