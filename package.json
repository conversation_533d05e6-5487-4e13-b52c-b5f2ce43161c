{"name": "playwright-dynamic-runner", "version": "1.0.0", "description": "基于 Playwright 的动态执行系统，支持边自动化操作边执行的功能", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "node test/test-runner.js", "install-playwright": "npx playwright install", "install-deps": "npm install && npm run install-playwright"}, "keywords": ["playwright", "automation", "testing", "dynamic-execution", "hot-reload"], "author": "Your Name", "license": "MIT", "dependencies": {"@playwright/test": "^1.54.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "playwright": "^1.40.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "your-repository-url"}, "bugs": {"url": "your-repository-url/issues"}, "homepage": "your-repository-url#readme"}