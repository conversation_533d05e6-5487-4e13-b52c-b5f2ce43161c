const { chromium } = require('playwright');
const { expect } = require('@playwright/test');

/**
 * Playwright 执行器
 * 负责管理浏览器实例和执行自动化代码
 */
class PlaywrightExecutor {
  constructor() {
    this.browser = null;
    this.context = null;
    this.page = null;
    this.isInitialized = false;
    this.executionHistory = [];
  }

  /**
   * 初始化浏览器
   * @param {boolean} headless - 是否无头模式
   * @param {string} url - 初始URL
   */
  async init(headless = false, url = 'about:blank') {
    try {
      console.log('正在初始化 Playwright 浏览器...');
      
      // 启动浏览器
      this.browser = await chromium.launch({
        headless: headless,
        devtools: !headless, // 非无头模式下开启开发者工具
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          // '--disable-dev-shm-usage',
          '--disable-web-security',
          '--allow-running-insecure-content'
        ]
      });

      // 创建上下文
      this.context = await this.browser.newContext({
        viewport: { width: 1440, height: 900 },
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      });

      // 创建页面
      this.page = await this.context.newPage();
      
      // 设置默认超时
      this.page.setDefaultTimeout(15000);
      this.page.setDefaultNavigationTimeout(15000);

      // 导航到初始URL
      if (url && url !== 'about:blank') {
        await this.page.goto(url);
      }

      this.isInitialized = true;
      console.log('Playwright 浏览器初始化成功');
      
      return {
        success: true,
        message: '浏览器初始化成功'
      };
    } catch (error) {
      console.error('浏览器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行步骤代码
   * @param {string} code - 要执行的代码
   * @param {string} stepId - 步骤ID
   */
  async executeStep(code, stepId) {
    if (!this.isInitialized) {
      throw new Error('浏览器未初始化，请先调用 init() 方法');
    }

    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      console.log(`开始执行步骤 ${stepId}:`, code);

      // 创建执行上下文，提供常用的对象和方法
      const executionContext = {
        page: this.page,
        context: this.context,
        browser: this.browser,
        // 测试断言
        expect: expect,
        // 常用的 Playwright 方法
        goto: (url) => this.page.goto(url),
        click: (selector) => this.page.click(selector),
        fill: (selector, value) => this.page.fill(selector, value),
        type: (selector, text) => this.page.type(selector, text),
        waitForSelector: (selector) => this.page.waitForSelector(selector),
        waitForTimeout: (timeout) => this.page.waitForTimeout(timeout),
        screenshot: (options) => this.page.screenshot(options),
        evaluate: (fn) => this.page.evaluate(fn),
        // 工具方法
        console: {
          log: (...args) => console.log(`[步骤 ${stepId}]`, ...args),
          error: (...args) => console.error(`[步骤 ${stepId}]`, ...args),
          warn: (...args) => console.warn(`[步骤 ${stepId}]`, ...args)
        },
        // 等待方法
        sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
        waitForElement: async (selector, timeout = 5000) => {
          return await this.page.waitForSelector(selector, { timeout });
        },
        // 获取元素信息
        getText: async (selector) => {
          return await this.page.textContent(selector);
        },
        getValue: async (selector) => {
          return await this.page.inputValue(selector);
        },
        getAttribute: async (selector, name) => {
          return await this.page.getAttribute(selector, name);
        },
        // 检查元素状态
        isVisible: async (selector) => {
          return await this.page.isVisible(selector);
        },
        isEnabled: async (selector) => {
          return await this.page.isEnabled(selector);
        },
        isChecked: async (selector) => {
          return await this.page.isChecked(selector);
        }
      };

      // 创建安全的执行函数
      const executeFunction = new Function(
        'context',
        `
        const {
          page, context: browserContext, browser, expect, goto, click, fill, type,
          waitForSelector, waitForTimeout, screenshot, evaluate, console,
          sleep, waitForElement, getText, getValue, getAttribute,
          isVisible, isEnabled, isChecked
        } = context;

        return (async () => {
          ${code}
        })();
        `
      );

      // 执行代码
      result = await executeFunction(executionContext);

      // 记录执行历史
      const execution = {
        stepId,
        code,
        result,
        startTime,
        endTime: Date.now(),
        duration: Date.now() - startTime,
        success: true,
        timestamp: new Date().toISOString()
      };
      
      this.executionHistory.push(execution);
      console.log(`步骤 ${stepId} 执行成功，耗时 ${execution.duration}ms`);

      return {
        success: true,
        result: result,
        duration: execution.duration,
        timestamp: execution.timestamp
      };

    } catch (err) {
      error = err;
      console.error(`步骤 ${stepId} 执行失败:`, err);

      // 记录失败的执行历史
      const execution = {
        stepId,
        code,
        error: err.message,
        stack: err.stack,
        startTime,
        endTime: Date.now(),
        duration: Date.now() - startTime,
        success: false,
        timestamp: new Date().toISOString()
      };
      
      this.executionHistory.push(execution);

      throw new Error(`步骤执行失败: ${err.message}`);
    }
  }

  /**
   * 获取浏览器信息
   */
  async getBrowserInfo() {
    if (!this.isInitialized) {
      return null;
    }

    try {
      const url = this.page.url();
      const title = await this.page.title();
      const viewport = this.page.viewportSize();

      return {
        url,
        title,
        viewport,
        isConnected: this.browser.isConnected(),
        executionCount: this.executionHistory.length,
        lastExecution: this.executionHistory.length > 0 ? 
          this.executionHistory[this.executionHistory.length - 1] : null
      };
    } catch (error) {
      console.error('获取浏览器信息失败:', error);
      return null;
    }
  }

  /**
   * 获取执行历史
   */
  getExecutionHistory() {
    return this.executionHistory;
  }

  /**
   * 清除执行历史
   */
  clearExecutionHistory() {
    this.executionHistory = [];
  }

  /**
   * 重置浏览器状态
   */
  async reset() {
    try {
      if (this.page) {
        await this.page.goto('about:blank');
        console.log('浏览器状态已重置');
      }
      this.clearExecutionHistory();
    } catch (error) {
      console.error('重置浏览器状态失败:', error);
      throw error;
    }
  }

  /**
   * 创建新页面
   */
  async newPage() {
    if (!this.isInitialized) {
      throw new Error('浏览器未初始化');
    }

    try {
      const newPage = await this.context.newPage();
      return newPage;
    } catch (error) {
      console.error('创建新页面失败:', error);
      throw error;
    }
  }

  /**
   * 切换到指定页面
   */
  async switchToPage(pageIndex = 0) {
    if (!this.isInitialized) {
      throw new Error('浏览器未初始化');
    }

    try {
      const pages = this.context.pages();
      if (pageIndex >= 0 && pageIndex < pages.length) {
        this.page = pages[pageIndex];
        await this.page.bringToFront();
        console.log(`已切换到页面 ${pageIndex}`);
        return this.page;
      } else {
        throw new Error(`页面索引 ${pageIndex} 超出范围`);
      }
    } catch (error) {
      console.error('切换页面失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有页面
   */
  getAllPages() {
    if (!this.isInitialized) {
      return [];
    }
    return this.context.pages();
  }

  /**
   * 关闭浏览器
   */
  async close() {
    try {
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
        this.context = null;
        this.page = null;
        this.isInitialized = false;
        console.log('浏览器已关闭');
      }
    } catch (error) {
      console.error('关闭浏览器失败:', error);
      throw error;
    }
  }

  /**
   * 检查是否已初始化
   */
  isReady() {
    return this.isInitialized && this.browser && this.browser.isConnected();
  }
}

module.exports = PlaywrightExecutor;
